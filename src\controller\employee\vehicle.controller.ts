import { Body, Controller, Get, Inject, Put } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Validate } from '@midwayjs/validate';
import { TokenPayload } from '../../interface';
import { VehicleService } from '../../service/vehicle.service';
import { EmployeeService } from '../../service/employee.service';
import { CustomError } from '../../error/custom.error';
import { UpdateVehicleStatusDto } from '../../dto/employee-vehicle.dto';

@Controller('/employee/vehicle')
export class EmployeeVehicleController {
  @Inject()
  ctx: Context;

  @Inject()
  vehicleService: VehicleService;

  @Inject()
  employeeService: EmployeeService;

  @Get('/', { summary: '员工端获取绑定车辆信息' })
  async getVehicle() {
    // 获取当前登录的员工信息
    const currentUser = this.ctx.state.user as TokenPayload;
    if (!currentUser || currentUser.userType !== 'employee') {
      throw new CustomError('请先登录员工账号');
    }

    // 获取员工信息
    const employee = await this.employeeService.findById(currentUser.userId);
    if (!employee) {
      throw new CustomError('员工信息不存在');
    }

    // 检查是否绑定了车辆
    if (!employee.vehicleId) {
      throw new CustomError('您还未绑定车辆');
    }

    // 获取车辆详细信息
    const vehicle = await this.vehicleService.getVehicleDetail(employee.vehicleId);
    if (!vehicle) {
      throw new CustomError('车辆信息不存在');
    }

    return vehicle;
  }

  @Put('/status', { summary: '员工端修改车辆状态' })
  @Validate()
  async updateStatus(@Body() body: UpdateVehicleStatusDto) {
    // 获取当前登录的员工信息
    const currentUser = this.ctx.state.user as TokenPayload;
    if (!currentUser || currentUser.userType !== 'employee') {
      throw new CustomError('请先登录员工账号');
    }

    // 获取员工信息
    const employee = await this.employeeService.findById(currentUser.userId);
    if (!employee) {
      throw new CustomError('员工信息不存在');
    }

    // 检查是否绑定了车辆
    if (!employee.vehicleId) {
      throw new CustomError('您还未绑定车辆');
    }

    // 验证车辆是否存在
    const vehicle = await this.vehicleService.findById(employee.vehicleId);
    if (!vehicle) {
      throw new CustomError('车辆信息不存在');
    }

    // 更新车辆状态，同时记录更新人和更新时间
    await this.vehicleService.updateVehicleInfo(
      employee.vehicleId,
      currentUser.userId,
      { status: body.status }
    );

    return {
      message: '车辆状态更新成功',
      status: body.status,
      updatedAt: new Date(),
    };
  }
}
