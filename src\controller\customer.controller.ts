import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { CustomerService } from '../service/customer.service';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';
import { DictionaryAttributes, PetAttributes, Customer } from '../entity';
import { CustomerAddressAttrs } from '../entity/address.entity';
import { MembershipCardOrderService } from '../service/membership-card-order.service';
import { CustomerMembershipCardService } from '../service/customer-membership-card.service';
import { CouponOrderService } from '../service/coupon-order.service';
import { CustomerCouponService } from '../service/customer-coupon.service';
import { ReviewService } from '../service/review.service';
import { CreateReviewDto, UpdateReviewDto } from '../dto/review.dto';
import { ComplaintService } from '../service/complaint.service';
import { CreateComplaintDto, UpdateComplaintDto } from '../dto/complaint.dto';
import { CreateOrderData, Address_Weapp } from '../interface';
// import { generateUniqueCode } from '../common/Utils';

@Controller('/customers')
export class CustomerController {
  @Inject()
  ctx: Context;

  @Inject()
  service: CustomerService;

  @Inject()
  membershipCardOrderService: MembershipCardOrderService;

  @Inject()
  customerMembershipCardService: CustomerMembershipCardService;

  @Inject()
  couponOrderService: CouponOrderService;

  @Inject()
  customerCouponService: CustomerCouponService;

  @Inject()
  reviewService: ReviewService;

  @Inject()
  complaintService: ComplaintService;

  @Get('/', { summary: '查询客户列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }

    // phone和nickname支持模糊查询
    if (queryInfo.phone) {
      queryInfo.phone = {
        [Op.like]: `%${queryInfo.phone}%`,
      };
    }
    if (queryInfo.nickname) {
      queryInfo.nickname = {
        [Op.like]: `%${queryInfo.nickname}%`,
      };
    }

    const res = await this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      include: ['pets', 'orders'],
      order: [['createdAt', 'DESC']],
    });
    // // TODO 后期删除
    // for (const c of res.list || []) {
    //   c.promotionCode = generateUniqueCode();
    //   await c.save();
    // }
    return res;
  }

  @Get('/:id', { summary: '按ID查询客户' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定客户');
    }
    return res;
  }

  @Post('/', { summary: '新增客户' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新客户' })
  async update(@Param('id') id: string, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除客户' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }

  @Get('/:id/pets', { summary: '获取客户的宠物列表' })
  async getPets(@Param('id') id: number) {
    return await this.service.getPets(id);
  }

  @Post('/:id/pet', { summary: '新增宠物' })
  async addPet(
    @Param('id') id: number,
    @Body() body: Omit<PetAttributes, 'id'>
  ) {
    return await this.service.addPet(id, body);
  }

  @Put('/:id/pet/:petId', { summary: '更新宠物' })
  async updatePet(
    @Param('id') id: number,
    @Param('petId') petId: number,
    @Body() body: Omit<PetAttributes, 'id'>
  ) {
    return await this.service.updatePet(id, petId, body);
  }

  @Del('/:id/pet/:petId', { summary: '删除宠物' })
  async deletePet(@Param('id') id: number, @Param('petId') petId: number) {
    return await this.service.deletePet(id, petId);
  }

  @Get('/:id/addresses', { summary: '获取客户的地址列表' })
  async getAddresses(@Param('id') id: number) {
    return await this.service.getAddressList(id);
  }

  @Get('/:id/pets/:petId/last-service-time', {
    summary: '查询指定宠物最后一次洗护完成时间',
  })
  async getLastServiceTime(
    @Param('id') id: number,
    @Param('petId') petId: number
  ) {
    return await this.service.getLastServiceTime(id, petId);
  }

  @Get('/:id/addresses/default', { summary: '获取客户的默认地址' })
  async getDefaultAddress(@Param('id') id: number) {
    return await this.service.getDefaultAddress(id);
  }

  @Post('/:id/address', { summary: '新增地址' })
  async addAddress(@Param('id') id: number, @Body() body: Address_Weapp) {
    const info: Omit<CustomerAddressAttrs, 'id'> = {
      customerId: id,
      contactName: body.contactName,
      contactPhone: body.contactPhone,
      // provinceCode: body.addressCode.slice(0, 2),
      // cityCode: body.addressCode.slice(2, 4),
      // districtCode: body.addressCode.slice(4, 6),
      // provinceName: body.addressArea.split(',')[0],
      // cityName: body.addressArea.split(',')[1],
      // districtName: body.addressArea.split(',')[2],
      provinceCode: '',
      cityCode: '',
      districtCode: '',
      provinceName: '',
      cityName: '',
      districtName: '',
      detailAddress: body.addressText,
      // longitude: Number(body.longitude),
      // latitude: Number(body.latitude),
      isDefault: Boolean(Number(body.isDefault)),
      remark: body.remark,
    };
    const res = await this.service.addAddress(id, info);
    if (info.isDefault) {
      await this.service.setDefaultAddress(id, res.id);
    }
    return res;
  }
  @Put('/:id/address/:addressId', { summary: '更新地址' })
  async updateAddress(
    @Param('id') id: number,
    @Param('addressId') addressId: number,
    @Body() body: Partial<Address_Weapp>
  ) {
    const info: Partial<CustomerAddressAttrs> = {};
    if (body.contactName) {
      info.contactName = body.contactName;
    }
    if (body.contactPhone) {
      info.contactPhone = body.contactPhone;
    }
    if (body.addressCode) {
      info.provinceCode = body.addressCode.slice(0, 2);
    }
    if (body.addressCode) {
      info.cityCode = body.addressCode.slice(2, 4);
    }
    if (body.addressCode) {
      info.districtCode = body.addressCode.slice(4, 6);
    }
    if (body.addressArea) {
      info.provinceName = body.addressArea.split(',')[0];
    }
    if (body.addressArea) {
      info.cityName = body.addressArea.split(',')[1];
    }
    if (body.addressArea) {
      info.districtName = body.addressArea.split(',')[2];
    }
    if (body.addressText) {
      info.detailAddress = body.addressText;
    }
    if (body.longitude) {
      info.longitude = Number(body.longitude);
    }
    if (body.latitude) {
      info.latitude = Number(body.latitude);
    }
    if (body.remark) {
      info.remark = body.remark;
    }
    if (body.isDefault) {
      info.isDefault = Boolean(Number(body.isDefault));
    }
    const res = await this.service.updateAddress(id, addressId, info);
    if (info.isDefault) {
      await this.service.setDefaultAddress(id, addressId);
    }
    return res;
  }

  @Del('/:id/address/:addressId', { summary: '删除地址' })
  async deleteAddress(
    @Param('id') id: number,
    @Param('addressId') addressId: number
  ) {
    return await this.service.deleteAddress(id, addressId);
  }

  @Put('/:id/address/:addressId/default', { summary: '设置默认地址' })
  async setDefaultAddress(
    @Param('id') id: number,
    @Param('addressId') addressId: number
  ) {
    return await this.service.setDefaultAddress(id, addressId);
  }

  @Get('/:customerId/orders', { summary: '获取客户的订单列表' })
  async getOrders(
    @Param('customerId') customerId: number,
    @Query() query: { currentPage?: number; pageSize?: number; status?: string }
  ) {
    const { currentPage, pageSize, status } = query;
    return await this.service.getOrders({
      customerId,
      status: status
        ? (status.split(',') as unknown[] as DictionaryAttributes[])
        : undefined,
      currentPage,
      pageSize,
    });
  }

  @Get('/:id/order/:orderId', { summary: '获取客户的订单详情' })
  async getOrder(@Param('id') id: number, @Param('orderId') orderId: number) {
    return await this.service.getOrder(id, orderId);
  }

  @Get('/:customerId/order/:sn/status', { summary: '获取订单状态' })
  async getOrderStatus(
    @Param('customerId') customerId: number,
    @Param('sn') sn: string
  ) {
    return await this.service.getOrderStatus(customerId, sn);
  }

  @Post('/:customerId/order', { summary: '创建订单' })
  async createOrder(
    @Param('customerId') customerId: number,
    @Body() body: CreateOrderData
  ) {
    // 验证必填的地址字段
    if (!body.address) {
      throw new CustomError('服务地址不能为空', 400);
    }
    if (!body.addressDetail) {
      throw new CustomError('服务地址详情不能为空', 400);
    }
    if (body.longitude === undefined || body.latitude === undefined) {
      throw new CustomError('服务地址坐标不能为空', 400);
    }

    // 验证订单明细中的说明图片数量
    for (const detail of body.orderDetails) {
      if (detail.remarkPhotos && detail.remarkPhotos.length > 3) {
        throw new CustomError('每个服务项目的说明图片最多只能上传3张', 400);
      }
    }

    // 确保使用路径参数中的customerId
    body.customerId = customerId;

    return await this.service.createOrder(customerId, body);
  }

  @Post('/:customerId/order/:sn/pay', { summary: '支付订单' })
  async payOrder(
    @Param('customerId') customerId: number,
    @Param('sn') sn: string
  ) {
    return await this.service.payOrder(customerId, sn);
  }

  // 申请退款
  @Post('/:customerId/applyRefund/:sn', { summary: '申请退款' })
  async refund(
    @Param('customerId') customerId: number,
    @Param('sn') sn: string
  ) {
    const res = await this.service.applyRefund(customerId, sn);
    return res;
  }

  @Del('/:customerId/order/:orderId', { summary: '取消订单' })
  async cancelOrder(
    @Param('customerId') customerId: number,
    @Param('orderId') orderId: number
  ) {
    return await this.service.cancelOrder(customerId, orderId);
  }

  // 权益卡相关接口
  @Get('/:customerId/membership-cards', { summary: '获取客户的权益卡列表' })
  async getMembershipCards(@Param('customerId') customerId: number) {
    return await this.customerMembershipCardService.findByCustomerId(
      customerId
    );
  }

  @Get('/:customerId/valid-membership-cards', {
    summary: '获取客户的有效权益卡列表',
  })
  async getValidMembershipCards(@Param('customerId') customerId: number) {
    return await this.customerMembershipCardService.findValidCards(customerId);
  }

  @Get('/:customerId/available-membership-cards', {
    summary: '获取可用的权益卡',
  })
  async getAvailableMembershipCards(
    @Param('customerId') customerId: number,
    @Query()
    query: {
      serviceId?: number;
      amount?: number;
    }
  ) {
    const { serviceId, amount } = query;
    return await this.customerMembershipCardService.getAvailableMembershipCards(
      customerId,
      serviceId ? Number(serviceId) : undefined,
      amount ? Number(amount) : undefined
    );
  }

  @Post('/:customerId/membership-card-order', { summary: '创建权益卡订单' })
  async createMembershipCardOrder(
    @Param('customerId') customerId: number,
    @Body() body: { cardTypeId: number; remark?: string }
  ) {
    if (!body.cardTypeId) {
      throw new CustomError('权益卡类型ID不能为空');
    }
    return await this.membershipCardOrderService.createOrder(
      customerId,
      body.cardTypeId,
      body.remark
    );
  }

  @Post('/:customerId/membership-card-order/:sn/pay', {
    summary: '支付权益卡订单',
  })
  async payMembershipCardOrder(
    @Param('customerId') customerId: number,
    @Param('sn') sn: string
  ) {
    return await this.membershipCardOrderService.payOrder(customerId, sn);
  }

  @Put('/:customerId/membership-card-order/:sn/cancel', {
    summary: '取消权益卡订单',
  })
  async cancelMembershipCardOrder(
    @Param('customerId') customerId: number,
    @Param('sn') sn: string
  ) {
    return await this.membershipCardOrderService.cancelOrder(customerId, sn);
  }

  @Get('/:customerId/membership-card-orders', {
    summary: '获取客户的权益卡订单列表',
  })
  async getMembershipCardOrders(
    @Param('customerId') customerId: number,
    @Query('status') status: string,
    @Query('current') current: number,
    @Query('pageSize') pageSize: number
  ) {
    let statusArray = [];
    if (status) {
      statusArray = status.split(',');
    }
    return await this.membershipCardOrderService.findCustomerOrders(
      customerId,
      statusArray,
      current,
      pageSize
    );
  }

  // 代金券相关接口
  @Get('/:customerId/coupons', { summary: '获取客户的代金券列表' })
  async getCoupons(@Param('customerId') customerId: number) {
    return await this.customerCouponService.findByCustomerId(customerId);
  }

  @Get('/:customerId/available-coupons', { summary: '获取可用的优惠券' })
  async getAvailableCoupons(
    @Param('customerId') customerId: number,
    @Query()
    query: {
      serviceId?: number;
      amount?: number;
    }
  ) {
    const { serviceId, amount } = query;
    return await this.customerCouponService.getAvailableCoupons(
      customerId,
      serviceId ? Number(serviceId) : undefined,
      amount ? Number(amount) : undefined
    );
  }

  @Post('/:customerId/coupon-order', { summary: '创建代金券订单' })
  async createCouponOrder(
    @Param('customerId') customerId: number,
    @Body() body: { couponId: number; remark?: string }
  ) {
    if (!body.couponId) {
      throw new CustomError('代金券ID不能为空');
    }
    return await this.couponOrderService.createOrder(
      customerId,
      body.couponId,
      body.remark
    );
  }

  @Get('/:customerId/employees', { summary: '获取可选择的员工列表' })
  async getAvailableEmployees(
    @Param('customerId') customerId: number,
    @Query('serviceTypeId') serviceTypeId?: number,
    @Query('current') current = 1,
    @Query('pageSize') pageSize = 10,
    @Query('name') name?: string
  ) {
    return await this.service.getAvailableEmployees(
      customerId,
      serviceTypeId,
      Number(current) || 1,
      Number(pageSize) || 10,
      name
    );
  }

  @Post('/:customerId/coupon-order/:sn/pay', { summary: '支付代金券订单' })
  async payCouponOrder(
    @Param('customerId') customerId: number,
    @Param('sn') sn: string
  ) {
    return await this.couponOrderService.payOrder(customerId, sn);
  }

  @Put('/:customerId/coupon-order/:sn/cancel', { summary: '取消代金券订单' })
  async cancelCouponOrder(
    @Param('customerId') customerId: number,
    @Param('sn') sn: string
  ) {
    return await this.couponOrderService.cancelOrder(customerId, sn);
  }

  @Get('/:customerId/coupon-orders', { summary: '获取客户的代金券订单列表' })
  async getCouponOrders(
    @Param('customerId') customerId: number,
    @Query('status') status: string,
    @Query('current') current: number,
    @Query('pageSize') pageSize: number
  ) {
    let statusArray = [];
    if (status) {
      statusArray = status.split(',');
    }
    return await this.couponOrderService.findCustomerOrders(
      customerId,
      statusArray,
      current,
      pageSize
    );
  }

  /** *********************评价相关接口**************************** */
  /**
   * 创建评价接口
   * POST /{customerId}/review
   */
  @Post('/:customerId/review', { summary: '创建评价' })
  async createReview(
    @Param('customerId') customerId: number,
    @Body() createDto: CreateReviewDto
  ) {
    const { orderId, rating, comment, photoURLs } = createDto;

    const review = await this.reviewService.createReview(
      customerId,
      orderId,
      rating,
      comment,
      photoURLs
    );

    return review;
  }

  /**
   * 根据订单ID获取评价接口
   * GET /orders/{orderId}/review
   */
  @Get('/orders/:orderId/review', { summary: '根据订单ID获取评价' })
  async getReviewByOrderId(@Param('orderId') orderId: number) {
    const review = await this.reviewService.getReviewByOrderId(orderId);

    if (!review) {
      return {
        code: 404,
        message: '该订单暂无评价',
        data: null,
      };
    }

    return review;
  }

  /**
   * 获取客户评价列表接口
   * GET /{customerId}/reviews
   */
  @Get('/:customerId/reviews', { summary: '获取客户评价列表' })
  async getCustomerReviews(
    @Param('customerId') customerId: number,
    @Query('page') page = 1,
    @Query('pageSize') pageSize = 10
  ) {
    const result = await this.reviewService.getCustomerReviews(
      customerId,
      page,
      pageSize
    );

    return {
      total: result.total,
      page,
      pageSize,
      totalPages: Math.ceil(result.total / pageSize),
      list: result.list,
    };
  }

  /**
   * 获取服务评价列表接口
   * GET /services/{serviceId}/reviews
   */
  @Get('/services/:serviceId/reviews', { summary: '获取服务评价列表' })
  async getServiceReviews(
    @Param('serviceId') serviceId: number,
    @Query('page') page = 1,
    @Query('pageSize') pageSize = 10
  ) {
    const result = await this.reviewService.getServiceReviews(
      serviceId,
      page,
      pageSize
    );

    return {
      total: result.total,
      page,
      pageSize,
      totalPages: Math.ceil(result.total / pageSize),
      averageRating: result.averageRating,
      ratingDistribution: result.ratingDistribution,
      list: result.list,
    };
  }

  /**
   * 更新评价接口
   * PUT /{customerId}/review/{reviewId}
   */
  @Put('/:customerId/review/:reviewId', { summary: '更新评价' })
  async updateReview(
    @Param('customerId') customerId: number,
    @Param('reviewId') reviewId: number,
    @Body() updateDto: UpdateReviewDto
  ) {
    const review = await this.reviewService.updateReview(
      customerId,
      reviewId,
      updateDto
    );

    return review;
  }

  /**
   * 删除评价接口
   * DELETE /{customerId}/review/{reviewId}
   */
  @Del('/:customerId/review/:reviewId', { summary: '删除评价' })
  async deleteReview(
    @Param('customerId') customerId: number,
    @Param('reviewId') reviewId: number
  ) {
    await this.reviewService.deleteReview(customerId, reviewId);

    return true;
  }

  /** *********************投诉建议相关接口**************************** */
  /**
   * 创建投诉建议接口
   * POST /{customerId}/complaint
   */
  @Post('/:customerId/complaint', { summary: '创建投诉建议' })
  async createComplaint(
    @Param('customerId') customerId: number,
    @Body() createDto: CreateComplaintDto
  ) {
    // 验证必填字段的业务逻辑
    if (createDto.subCategory === 'order' && !createDto.orderId) {
      throw new CustomError('订单投诉必须提供订单ID', 400);
    }

    if (createDto.subCategory === 'employee' && !createDto.employeeId) {
      throw new CustomError('人员投诉必须提供员工ID', 400);
    }

    const complaint = await this.complaintService.createComplaint(
      customerId,
      createDto
    );

    return complaint;
  }

  /**
   * 获取客户投诉建议列表接口
   * GET /{customerId}/complaints
   */
  @Get('/:customerId/complaints', { summary: '获取客户投诉建议列表' })
  async getCustomerComplaints(
    @Param('customerId') customerId: number,
    @Query('page') page = 1,
    @Query('pageSize') pageSize = 10,
    @Query('category') category?: 'complaint' | 'suggestion',
    @Query('status') status?: 'pending' | 'processing' | 'resolved' | 'closed'
  ) {
    const result = await this.complaintService.getCustomerComplaints(
      customerId,
      page,
      pageSize
    );

    // 如果有过滤条件，进行过滤
    let filteredList = result.list;
    if (category) {
      filteredList = filteredList.filter(item => item.category === category);
    }
    if (status) {
      filteredList = filteredList.filter(item => item.status === status);
    }

    return {
      total: filteredList.length,
      page,
      pageSize,
      totalPages: Math.ceil(filteredList.length / pageSize),
      list: filteredList.slice((page - 1) * pageSize, page * pageSize),
    };
  }

  /**
   * 更新投诉建议接口
   * PUT /{customerId}/complaint/{complaintId}
   */
  @Put('/:customerId/complaint/:complaintId', { summary: '更新投诉建议' })
  async updateComplaint(
    @Param('customerId') customerId: number,
    @Param('complaintId') complaintId: number,
    @Body() updateDto: UpdateComplaintDto
  ) {
    // 验证必填字段的业务逻辑
    if (updateDto.subCategory === 'order' && !updateDto.orderId) {
      throw new CustomError('订单投诉必须提供订单ID', 400);
    }

    if (updateDto.subCategory === 'employee' && !updateDto.employeeId) {
      throw new CustomError('人员投诉必须提供员工ID', 400);
    }

    const complaint = await this.complaintService.updateComplaint(
      customerId,
      complaintId,
      updateDto
    );

    return complaint;
  }

  /**
   * 删除投诉建议接口
   * DELETE /{customerId}/complaint/{complaintId}
   */
  @Del('/:customerId/complaint/:complaintId', { summary: '删除投诉建议' })
  async deleteComplaint(
    @Param('customerId') customerId: number,
    @Param('complaintId') complaintId: number
  ) {
    await this.complaintService.deleteComplaint(complaintId);

    return true;
  }

  /**
   * 根据订单ID获取投诉建议接口
   * GET /orders/{orderId}/complaints
   */
  @Get('/orders/:orderId/complaints', { summary: '根据订单ID获取投诉建议' })
  async getComplaintsByOrderId(@Param('orderId') orderId: number) {
    const complaints = await this.complaintService.findAll({
      query: { orderId },
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone', 'avatar'],
        },
      ],
    });

    return complaints;
  }
}
