# 员工端车辆相关API接口

## 车辆状态枚举
```typescript
enum VehicleStatus {
  空闲 = '空闲',
  服务中 = '服务中'
}
```

## 1. 员工端车辆管理接口

### 1.1 获取绑定车辆信息
- **接口**: `GET /employee/vehicle`
- **描述**: 员工端获取自己绑定的车辆详细信息
- **权限**: 需要员工端登录
- **参数**: 无
- **返回示例**:
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "plateNumber": "京A12345",
    "latitude": 39.908823,
    "longitude": 116.397470,
    "status": "空闲",
    "mileage": 15000.50,
    "appearance": "车辆外观良好，无明显划痕",
    "insuranceExpiry": "2024-12-31T16:00:00.000Z",
    "licenseExpiry": "2025-06-30T16:00:00.000Z",
    "supplies": "洗车工具齐全，包括洗车液、毛巾、吸尘器等",
    "lastSubmittedAt": "2024-01-15T08:30:00.000Z",
    "lastSubmittedBy": 1,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-15T08:30:00.000Z",
    "lastSubmittedEmployee": {
      "id": 1,
      "name": "张三",
      "phone": "13800138001"
    }
  }
}
```

### 1.2 修改车辆状态
- **接口**: `PUT /employee/vehicle/status`
- **描述**: 员工端修改自己绑定车辆的状态
- **权限**: 需要员工端登录
- **请求参数**:
```json
{
  "status": "服务中"
}
```

- **参数说明**:
  - `status` (string, 必填): 车辆状态，可选值：'空闲'、'服务中'

- **返回示例**:
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "message": "车辆状态更新成功",
    "status": "服务中",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 401 | 请先登录员工账号 | 未登录或登录类型不是员工 |
| 404 | 员工信息不存在 | 员工账号不存在 |
| 400 | 您还未绑定车辆 | 员工未分配车辆 |
| 404 | 车辆信息不存在 | 车辆记录不存在 |
| 400 | 参数验证失败 | 状态值不在允许范围内 |

## 业务规则

1. **权限验证**: 只有登录的员工才能访问这些接口
2. **车辆绑定**: 员工必须已经绑定车辆才能进行状态修改
3. **状态限制**: 车辆状态只能在'空闲'和'服务中'之间切换
4. **记录追踪**: 每次状态更新都会记录更新时间和更新人员
5. **数据完整性**: 系统会验证员工、车辆的存在性

## 使用场景

1. **开始服务**: 员工接单后，将车辆状态从'空闲'改为'服务中'
2. **结束服务**: 员工完成服务后，将车辆状态从'服务中'改为'空闲'
3. **状态查询**: 员工可以随时查看自己绑定车辆的详细信息和当前状态
