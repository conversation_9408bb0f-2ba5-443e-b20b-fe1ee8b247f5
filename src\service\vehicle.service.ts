import { Provide } from '@midwayjs/core';
import { Vehicle } from '../entity/vehicle.entity';
import { BaseService } from '../common/BaseService';
import { Employee } from '../entity/employee.entity';

@Provide()
export class VehicleService extends BaseService<Vehicle> {
  constructor() {
    super('车辆');
  }

  getModel() {
    return Vehicle;
  }

  async findByPlateNumber(plateNumber: string) {
    return await this.findOne({ where: { plateNumber } });
  }

  async updateLocation(id: number, latitude: number, longitude: number) {
    return await this.update({ id }, { latitude, longitude });
  }

  async updateStatus(id: number, status: string) {
    return await this.update({ id }, { status });
  }

  async getEmployee(vehicleId: number) {
    const vehicle = await Vehicle.findByPk(vehicleId, {
      include: [Employee],
    });
    return vehicle?.employee;
  }

  /**
   * 员工端更新车辆信息
   * @param vehicleId 车辆ID
   * @param employeeId 员工ID
   * @param updateData 更新数据
   */
  async updateVehicleInfo(
    vehicleId: number,
    employeeId: number,
    updateData: any
  ) {
    const now = new Date();
    const updateInfo = {
      ...updateData,
      lastSubmittedAt: now,
      lastSubmittedBy: employeeId,
    };

    return await this.update({ id: vehicleId }, updateInfo);
  }

  /**
   * 获取车辆详细信息（包含提交记录）
   * @param vehicleId 车辆ID
   */
  async getVehicleDetail(vehicleId: number) {
    const vehicle = await Vehicle.findByPk(vehicleId, {
      include: [
        {
          model: Employee,
          as: 'employee',
        },
      ],
    });

    if (!vehicle) {
      return null;
    }

    // 获取最后提交人信息
    let lastSubmittedEmployee = null;
    if (vehicle.lastSubmittedBy) {
      lastSubmittedEmployee = await Employee.findByPk(vehicle.lastSubmittedBy, {
        attributes: ['id', 'name', 'phone'],
      });
    }

    return {
      ...vehicle.toJSON(),
      lastSubmittedEmployee,
    };
  }

  /**
   * 员工端更新车辆状态
   * @param employeeId 员工ID
   * @param status 新状态
   */
  async updateVehicleStatusByEmployee(employeeId: number, status: string) {
    // 查找员工绑定的车辆
    const employee = await Employee.findByPk(employeeId, {
      attributes: ['id', 'vehicleId'],
    });

    if (!employee) {
      throw new Error('员工不存在');
    }

    if (!employee.vehicleId) {
      throw new Error('员工未绑定车辆');
    }

    // 验证车辆是否存在
    const vehicle = await Vehicle.findByPk(employee.vehicleId);
    if (!vehicle) {
      throw new Error('车辆不存在');
    }

    // 更新车辆状态
    return await this.updateVehicleInfo(employee.vehicleId, employeeId, {
      status,
    });
  }
}
